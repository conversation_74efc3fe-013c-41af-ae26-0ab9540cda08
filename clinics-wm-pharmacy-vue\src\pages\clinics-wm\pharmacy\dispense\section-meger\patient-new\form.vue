<!--药房-库房业务-药房发药-病区发药-按患者发药-->
<script setup lang="ts">
import { nextTick } from 'vue'

import {
  getSectionMegerReservedWbSeqidApi,
  stockReserveBySectionApi,
  releaseStockByWbSeqidApi,
  reqDeliverBySectionApi,
  finishedReqBySectionApi
} from "~/api/clinics_wm/wmreq.ts";
import WmBillArtTable from "./bill-art-table.vue";
import ReqArtLockedTable from './req-art-locked-table.vue'
import ReqArtUnlockTable from './req-art-unlock-table.vue'

const message = useMessage()
const emit = defineEmits(['close', 'refresh'])

const reqArtUnlockTableRef = ref()
const reqArtLockedTableRef = ref()
const wmBillDetailTableRef = ref()

interface FormState {
  deptCode?: string;
  sectionId?: number;
  sectionName?: string | null;
  visitId?: number;
  patientName?: string | null;
  finishedFlag?: number;
}

const fomrState = ref<FormState>({})
const activeKey = ref('1')
const btnLoading = ref(false)
const isViewRef = ref(false)
const reservedWbSeqids = ref(null)

// 各个表格的数据数量
const waitDeliverCount = ref(0)
const unlockCount = ref(0)
const lockedCount = ref(0)

// 用户手动切换tab的标志
const isUserManualSwitch = ref(false)
// 是否是初始加载
const isInitialLoad = ref(true)

// 计算属性处理 null 值
const patientNameValue = computed(() => fomrState.value.patientName || '')
const sectionNameValue = computed(() => fomrState.value.sectionName || '')

const init = async (deptCode: string, sectionId: number | null, sectionName: string | null, visitId: number | null, patientName: string | null, finishedFlag: number) => {
  if (!sectionId || !visitId) {
    fomrState.value = { deptCode, finishedFlag }
    reservedWbSeqids.value = null
    reqArtUnlockTableRef.value?.clearData?.()
    reqArtLockedTableRef.value?.clearData?.()
    wmBillDetailTableRef.value?.clearData?.()

    // 重置数据数量
    waitDeliverCount.value = 0
    unlockCount.value = 0
    lockedCount.value = 0

    // 重置默认选项卡到待出库明细
    activeKey.value = '2'
    return
  }

  fomrState.value = {
    deptCode,
    sectionId,
    sectionName,
    visitId,
    patientName,
    finishedFlag
  }
  isViewRef.value = finishedFlag === 1

  // 选中数据时，重置到待出库明细tab，优先查看待出库明细
  setActiveKeyInternal('2')

  // 重置标志，允许自动切换
  isUserManualSwitch.value = false
  isInitialLoad.value = true

  await reload()
}

const reload = async () => {
  btnLoading.value = true
  await loadReservedInfo()

  // 先查询待出库明细，再查询未锁定明细
  await wmBillDetailTableRef.value?.reload()
  await reqArtUnlockTableRef.value?.reload()
  await reqArtLockedTableRef.value?.reload()

  // 等待一个tick确保所有数据都已加载完成
  await nextTick()

  // 使用统一的数据加载完成处理方法
  onDataLoaded()

  btnLoading.value = false
}

const loadReservedInfo = async () => {
  const { data } = await getSectionMegerReservedWbSeqidApi({
    deptCode: fomrState.value.deptCode,
    sectionId: fomrState.value.sectionId,
    visitId: fomrState.value.visitId
  })
  reservedWbSeqids.value = data

  // 如果有数据，默认选中第一行并触发查询明细信息
  if (data && data.length > 0) {
    console.log('有锁定数据，默认选中第一行:', data[0])
    // 这里可以添加选中第一行的逻辑
  }
}

// 更新各个表格的数据数量
const updateTableCounts = () => {
  // 获取待出库明细数量
  const waitCount = wmBillDetailTableRef.value?.getDataCount?.() || 0
  waitDeliverCount.value = waitCount

  // 获取未锁定明细数量
  const unlockCountValue = reqArtUnlockTableRef.value?.getDataCount?.() || 0
  unlockCount.value = unlockCountValue

  // 获取已锁定明细数量
  const lockedCountValue = reqArtLockedTableRef.value?.getDataCount?.() || 0
  lockedCount.value = lockedCountValue

  console.log('数据数量更新:', {
    waitDeliverCount: waitDeliverCount.value,
    unlockCount: unlockCount.value,
    lockedCount: lockedCount.value,
    currentActiveKey: activeKey.value,
    refs: {
      wmBillDetailTableRef: !!wmBillDetailTableRef.value,
      reqArtUnlockTableRef: !!reqArtUnlockTableRef.value,
      reqArtLockedTableRef: !!reqArtLockedTableRef.value,
      wmBillDetailGetDataCount: !!wmBillDetailTableRef.value?.getDataCount,
      reqArtUnlockGetDataCount: !!reqArtUnlockTableRef.value?.getDataCount,
      reqArtLockedGetDataCount: !!reqArtLockedTableRef.value?.getDataCount
    }
  })
}

// 处理数据加载完成后的自动切换逻辑
const handleAutoTabSwitch = () => {
  // 只在初始加载时或非用户手动切换时进行自动切换
  if (!isUserManualSwitch.value) {
    let targetTab = activeKey.value

    // 自动切换tab逻辑：优先显示有数据的tab
    // 1. 如果待出库明细有数据，显示待出库明细
    // 2. 如果待出库明细没有数据，但未锁定明细有数据，显示未锁定明细
    // 3. 如果前两者都没有数据，但已锁定明细有数据，显示已锁定明细
    // 4. 如果待出库明细与未锁定明细都没有数据时，默认选中待出库明细tab页
    if (waitDeliverCount.value > 0) {
      targetTab = '2'
    } else if (unlockCount.value > 0) {
      targetTab = '1'
    } else if (lockedCount.value > 0) {
      targetTab = '3'
    } else if (isInitialLoad.value) {
      // 只在初始加载时设置默认tab，避免覆盖用户的手动选择
      targetTab = '2'
    }

    if (targetTab !== activeKey.value) {
      setActiveKeyInternal(targetTab)
      console.log('自动切换到tab:', targetTab)
    }
  } else {
    console.log('用户手动切换，跳过自动切换逻辑')
  }

  // 初始加载完成后，设置标志
  if (isInitialLoad.value) {
    isInitialLoad.value = false
  }
}

// 统一的数据加载完成处理方法
const onDataLoaded = () => {
  // 延迟一点时间确保所有组件都已更新
  setTimeout(() => {
    updateTableCounts()
    handleAutoTabSwitch()
  }, 100)
}

// 强制更新数据数量的方法（用于调试）
const forceUpdateCounts = () => {
  console.log('强制更新数据数量')
  updateTableCounts()
}

// 监听activeKey变化，检测用户手动切换
let isInternalChange = false
watch(activeKey, (newVal, oldVal) => {
  if (!isInternalChange && !isInitialLoad.value) {
    // 用户手动切换了tab
    isUserManualSwitch.value = true
    console.log('检测到用户手动切换tab:', oldVal, '->', newVal)
  }
})

// 内部切换activeKey的方法
const setActiveKeyInternal = (key: string) => {
  isInternalChange = true
  activeKey.value = key
  nextTick(() => {
    isInternalChange = false
  })
}

const lockedWaitDeliver = async () => {
  setActiveKeyInternal('2')
  console.log('lockedWaitDeliver', reservedWbSeqids.value)
  await loadReservedInfo()
  console.log('lockedWaitDeliver loadReservedInfo', reservedWbSeqids.value)
  await wmBillDetailTableRef.value?.reload(true)

  // 等待一个tick确保数据加载完成
  await nextTick()

  // 使用统一的数据加载完成处理方法
  onDataLoaded()
}

defineExpose({
  init
})
</script>

<template>
  <a-tabs v-model:activeKey="activeKey">
    <a-tab-pane key="2" v-if="!isViewRef">
      <template #tab>
        <span v-if="waitDeliverCount > 0">待出库明细({{ waitDeliverCount }})</span>
        <span v-else>待出库明细</span>
      </template>
      <wm-bill-art-table
          ref="wmBillDetailTableRef"
          :wbSeqids="reservedWbSeqids || []"
          :deptCode="fomrState.deptCode"
          :sectionId="fomrState.sectionId"
          @refresh="$emit('refresh')" />
    </a-tab-pane>
    <a-tab-pane key="1">
      <template #tab>
        <span v-if="unlockCount > 0">未锁定明细({{ unlockCount }})</span>
        <span v-else>未锁定明细</span>
      </template>
      <req-art-unlock-table
        ref="reqArtUnlockTableRef"
        :deptCode="fomrState.deptCode"
        :sectionId="fomrState.sectionId"
        :visitId="fomrState.visitId"
        @ok="reload"
        @locked="lockedWaitDeliver"
        @refresh="$emit('refresh')" />
    </a-tab-pane>
    <a-tab-pane key="3">
      <template #tab>
        <span v-if="lockedCount > 0">已锁定明细({{ lockedCount }})</span>
        <span v-else>已锁定明细</span>
      </template>
      <req-art-locked-table
        ref="reqArtLockedTableRef"
        :deptCode="fomrState.deptCode"
        :sectionId="fomrState.sectionId"
        :visitId="fomrState.visitId"
        :finishedFlag="fomrState.finishedFlag"
        @refresh="$emit('refresh')" />
    </a-tab-pane>
    <template #rightExtra>
      <a-space style="padding-right: 20px">
        <a-form-item label="患者姓名" name="patientName">
          <a-input v-model:value="patientNameValue" :disabled="true"/>
        </a-form-item>
        <a-form-item label="病区名称" name="sectionName">
          <a-input v-model:value="sectionNameValue" :disabled="true"/>
        </a-form-item>
      </a-space>
    </template>
  </a-tabs>
</template>

<style lang="less" scoped>
.bg-fff {
  background-color: #fff;
  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }
  :deep(.ant-tabs-nav-list) {
    margin-left: 10px;
  }
}
</style> 