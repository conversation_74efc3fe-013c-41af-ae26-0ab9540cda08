<script setup lang="ts">
import { ref, reactive, watchEffect, computed } from 'vue'
import type { PaginationProps } from "ant-design-vue";
import {
  sectionMegerArtDetailPageApi,
  sectionMegerArtListApi,
  sectionMegerPrintHeaderApi
} from "~/api/clinics_wm/wmreq.ts";
import { getPrintTemplateFullApi, PrintTemplateType } from "~/api/hip/printtemplate.ts";
import lodop from "~/components/lodop";

interface Props {
  deptCode?: string;
  sectionId?: number;
  visitId?: number;
  finishedFlag?: number;
}

const props = defineProps<Props>()
const showShortFlag = ref(false)

const emit = defineEmits(['dataLoaded'])

interface DetailRecord {
  wmReqid: string;
  wbSeqid: string;
  lineNo: number;
  artId: string;
  artName: string;
  artSpec: string;
  producer: string;
  bedNo: string;
  patientName: string;
  shortCells: number;
  cellsDelivered: number;
  cellsReserved: number;
  cellUnit: string;
  packUnit: string;
  miCode: string;
}

interface TableModel {
  loading: boolean;
  columns: Array<{
    title: string;
    dataIndex: string;
    width?: number;
    align?: 'left' | 'right' | 'center';
  }>;
  dataSource: DetailRecord[];
  dataSourceExtra: Record<string, any>;
  pagination: PaginationProps;
  customRow: (record: DetailRecord) => { style: { color: string } };
  loadDataSource: () => Promise<void>;
}

const detailTableModel = reactive<TableModel>({
  loading: false,
  columns: [{
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'right'
  }, {
    title: '申请单',
    dataIndex: 'wmReqid',
    width: 80,
    align: 'right'
  }, {
    title: '锁定单',
    dataIndex: 'wbSeqid',
    width: 80,
    align: 'right'
  }, {
    title: '品种ID',
    dataIndex: 'artId',
    width: 80,
    align: 'right'
  }, {
    title: '品名|规格|厂家',
    dataIndex: 'name',
    align: 'left'
  }, {
    title: '申领数量',
    dataIndex: 'shortCells',
    width: 75,
    align: 'right'
  }, {
    title: '已发药数',
    dataIndex: 'cellsDelivered',
    width: 75,
    align: 'right'
  }, {
    title: '已锁定数',
    dataIndex: 'cellsReserved',
    width: 75,
    align: 'right'
  }, {
    title: '医保编码',
    dataIndex: 'miCode',
    width: 150,
    align: 'left'
  }],
  dataSource: [],
  dataSourceExtra: {},
  loadDataSource: async () => {
    if (props.deptCode && props.visitId) {
      detailTableModel.loading = true
      try {
        const apiParams = {
          ...searchFormModel,
          deptCode: props.deptCode,
          sectionId: props.sectionId,
          visitId: props.visitId,
          lockedFlag: 2,
          showShortFlag: showShortFlag.value ? 1 : 0,
          pageNum: detailTableModel.pagination.current,
          pageSize: detailTableModel.pagination.pageSize,
        }

        const {data} = await sectionMegerArtDetailPageApi(apiParams)
        detailTableModel.dataSource = data.list
        detailTableModel.dataSourceExtra = data.extra || {}
        detailTableModel.pagination.total = data.total ?? 0

        console.log('patient-new 已锁定明细数据加载完成:', {
          dataCount: data.list?.length || 0,
          total: data.total,
          extra: data.extra,
          apiParams
        })

        // 发射数据加载完成事件
        emit('dataLoaded')
      } catch (err) {
        console.error('加载数据失败:', err)
      } finally {
        detailTableModel.loading = false
      }
    } else {
      detailTableModel.dataSource = []
      detailTableModel.dataSourceExtra = {}

      // 发射数据加载完成事件
      emit('dataLoaded')
    }
  },
  pagination: reactive<PaginationProps>({
    pageSize: 100,
    current: 1,
    total: 0,
    showQuickJumper: true,
    showSizeChanger: true,
    showTotal: (total: number) => `共：${total} 条`,
    onChange(current: number, pageSize: number) {
      detailTableModel.pagination.pageSize = pageSize
      detailTableModel.pagination.current = current
      detailTableModel.loadDataSource()
    },
  }),
  customRow: (record: DetailRecord) => {
    return {
      style: {
        color: record.shortCells > record.cellsDelivered + record.cellsReserved ? 'red' : '',
      }
    }
  }
})

const searchFormModel = reactive<{
  S_EQ_t_wm_req_detail__Art_ID?: string;
  S_LIKE_t_article__Art_Name?: string;
  S_LIKE_t_visit__Patient_Name?: string;
}>({})

// 合计
const tableSummary = computed(() => {
  if (!detailTableModel.dataSourceExtra || Object.keys(detailTableModel.dataSourceExtra).length === 0) {
    return null
  }

  return [{
    index: 0,
    colSpan: 5,
    label: '合计：',
    style: {}
  }, {
    index: 5,
    colSpan: 1,
    label: detailTableModel.dataSourceExtra.shortCellsSum || '',
    style: {
      textAlign: 'right'
    }
  }, {
    index: 6,
    colSpan: 1,
    label: detailTableModel.dataSourceExtra.cellsDeliveredSum || '',
    style: {
      textAlign: 'right'
    }
  }, {
    index: 7,
    colSpan: 1,
    label: detailTableModel.dataSourceExtra.cellsReservedSum || '',
    style: {
      textAlign: 'right'
    }
  }]
})

watchEffect(async () => {
  await detailTableModel.loadDataSource()
})

const reload = async () => {
  detailTableModel.pagination.current = 1
  await detailTableModel.loadDataSource()
}

const clearData = () => {
  detailTableModel.dataSource = []
  detailTableModel.dataSourceExtra = {}
  detailTableModel.pagination.total = 0
  detailTableModel.pagination.current = 1

  // 发射数据加载完成事件
  emit('dataLoaded')
}

const loadSectionMegerPrintHeader = async () => {
  const {data} = await sectionMegerPrintHeaderApi({
    deptCode: props.deptCode,
    sectionId: props.sectionId,
    visitId: props.visitId
  })
  return data
}

const handlePrintBill = async () => {
  const printHeader = await loadSectionMegerPrintHeader()
  const {data} = await sectionMegerArtListApi({
    deptCode: props.deptCode,
    sectionId: props.sectionId,
    S_EQ_t_wm_req_detail__visit_ID: props.visitId,
    allFlag: 1
  })
  const billData = {
    ...printHeader,
    details: data
  }
  const template = await getPrintTemplateFullApi(PrintTemplateType.section_meger_reqart)
  if (template.tempItems) {
    template.tempItems = JSON.parse(template.tempItems);
  }
  const printData = [billData]
  lodop.preview(template, printData)
}

// 获取数据数量的方法 - 使用 pagination.total 获取正确的总数统计
const getDataCount = () => {
  const count = detailTableModel.pagination.total
  console.log('patient-new 已锁定明细 getDataCount 被调用 - 使用 pagination.total:', count)
  return count
}

defineExpose({
  reload,
  clearData,
  getDataCount
})
</script>

<template>
  <base-table
    :loading="detailTableModel.loading"
    :columns="detailTableModel.columns"
    :height="600"
    :dataSource="detailTableModel.dataSource"
    :pagination="detailTableModel.pagination"
    :custom-row="detailTableModel.customRow"
    :summary="tableSummary"
    :rowKey="(item: DetailRecord) => item.wmReqid + '-' + item.lineNo">
    <template #btns>
      <a-space size="middle" class="m-l-10px">
        <a-space>
          <text>条目ID:</text>
          <a-input v-model:value="searchFormModel.S_EQ_t_wm_req_detail__Art_ID" style="width: 80px" @pressEnter="reload"/>
        </a-space>
        <a-space>
          <text>品名:</text>
          <a-input v-model:value="searchFormModel.S_LIKE_t_article__Art_Name" style="width: 150px" @pressEnter="reload"/>
        </a-space>
        <a-space>
          <text>患者:</text>
          <a-input v-model:value="searchFormModel.S_LIKE_t_visit__Patient_Name" style="width: 100px" @pressEnter="reload"/>
        </a-space>
        <a-button @click="handlePrintBill" v-if="props.sectionId">
          打印申领汇总单
        </a-button>
      </a-space>
    </template>
    <template #bodyCell="{ column, record, index }">
      <template v-if="column?.dataIndex === 'index'">
        {{ index + 1 }}
      </template>
      <template v-if="column?.dataIndex === 'name'">
        {{ record.artName }} {{ record.artSpec }}<br/>{{ record.producer }}
      </template>
      <template v-if="column?.dataIndex === 'shortCells'">
        <span v-if="record.shortCells">{{ record.shortCells }}{{ record.cellUnit }}</span>
      </template>
      <template v-if="column?.dataIndex === 'cellsDelivered'">
        <span v-if="record.cellsDelivered > 0">{{ record.cellsDelivered }}{{ record.cellUnit }}</span>
      </template>
      <template v-if="column?.dataIndex === 'cellsReserved'">
        <span v-if="record.cellsReserved > 0">{{ record.cellsReserved }}{{ record.cellUnit }}</span>
      </template>
    </template>
  </base-table>
</template>

<style lang="less" scoped>
.bg-fff {
  background-color: #fff;
  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }
  :deep(.ant-tabs-nav-list) {
    margin-left: 10px;
  }
}
</style> 