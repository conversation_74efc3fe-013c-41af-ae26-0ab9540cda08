<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import type { PaginationProps } from "ant-design-vue";
import { message } from 'ant-design-vue'
import {Modal} from "ant-design-vue";
import {createVNode} from "vue";
import {ExclamationCircleOutlined} from "@ant-design/icons-vue";
import { BillsTrackCode } from '@mh-wm/bills-track-code'
import '@mh-wm/bills-track-code/index.css'
import { SystemSettingEnum } from '@/enums/system-setting-enum.ts'
import { useUserStore } from '@/stores/user'

import {
  detailGroupByArtPageApi,
  detailGroupByArtPageByIdsApi,
  detailPageApi,
  sectionDeliveredDetailListApi
} from "~/api/clinics_wm/wmbill.ts";
import {reqDeliverBySectionApi, sectionMegerPrintHeaderApi} from "~/api/clinics_wm/wmreq.ts";
import {getPrintTemplateFullApi, PrintTemplateType} from "~/api/hip/printtemplate.ts";
import lodop from "~/components/lodop";

const props = defineProps({
  wbSeqids: { type: Object },
  deptCode: { type: String },
  sectionId: { type: Number }
})
const btnLoading = ref(false)

const userStore = useUserStore()
const scanTrackCode = computed(() => {
  const enable = userStore.getSystemSettingByCode(SystemSettingEnum.CW_SCAN_TRACK_CODE_AFTER_SECTION_DELIVER)
  return enable && Number(enable) === 1
})

const billsTrackCodeRef = ref()

interface DetailRecord {
  artId: string;
  artName: string;
  artSpec: string;
  producer: string;
  batchNo: string;
  totalPacks: number;
  totalCells: number;
  deptTotalPacks: number;
  deptTotalCells: number;
  packPrice: number;
  cellPrice: number;
  amount: number;
  miCode: string;
  packUnit: string;
  cellUnit: string;
  currentTotal: number;
}

interface TableModel {
  loading: boolean;
  columns: Array<{
    title: string;
    dataIndex: string;
    width?: number;
    align?: 'left' | 'right' | 'center';
  }>;
  dataSource: DetailRecord[];
  dataSourceExtra: Record<string, any>;
  pagination: PaginationProps;
  loadDataSource: () => Promise<void>;
}

const billDetailTableModel = reactive<TableModel>({
  loading: false,
  columns: [{
    title: '序号',
    dataIndex: 'index',
    width: 45,
    align: 'right'
  }, {
    title: '品种ID',
    dataIndex: 'artId',
    width: 75,
    align: 'right'
  }, {
    title: '品名|规格|厂家',
    dataIndex: 'name',
    width: 300,
    align: 'left'
  }, {
    title: '批号',
    dataIndex: 'batchNo',
    width: 90,
    align: 'right'
  }, {
    title: '发药数量',
    dataIndex: 'totalPacks',
    width: 80,
    align: 'right'
  }, {
    title: '库存量',
    dataIndex: 'currentTotal',
    width: 90,
    align: 'right'
  }, {
    title: '价格',
    dataIndex: 'packPrice',
    width: 70,
    align: 'right'
  }, {
    title: '金额',
    dataIndex: 'amount',
    width: 80,
    align: 'right'
  }, {
    title: '医保编码',
    dataIndex: 'miCode',
    width: 150,
    align: 'left'
  }],
  dataSource: [],
  dataSourceExtra: {},
  loadDataSource: async () => {
    if (props.wbSeqids && props.wbSeqids.length > 0) {
      billDetailTableModel.loading = true
      try {
        const {data} = await detailGroupByArtPageByIdsApi({
          ...searchFormModel,
          wbSeqids: props.wbSeqids,
          pageNum: billDetailTableModel.pagination.current,
          pageSize: billDetailTableModel.pagination.pageSize,
        })
        billDetailTableModel.dataSource = data.list
        billDetailTableModel.dataSourceExtra = data.extra
        billDetailTableModel.pagination.total = data.total ?? 0

        // 发射数据加载完成事件
        emit('dataLoaded')
      } catch (err) {
        console.error('加载数据失败:', err)
      } finally {
        billDetailTableModel.loading = false
      }
    } else {
      billDetailTableModel.dataSource = []
      billDetailTableModel.dataSourceExtra = {}

      // 发射数据加载完成事件
      emit('dataLoaded')
    }
  },
  pagination: reactive<PaginationProps>({
    pageSize: 100,
    current: 1,
    total: 0,
    showQuickJumper: true,
    showSizeChanger: true,
    showTotal: (total: number) => `共：${total} 条`,
    onChange(current: number, pageSize: number) {
      billDetailTableModel.pagination.pageSize = pageSize
      billDetailTableModel.pagination.current = current
      billDetailTableModel.loadDataSource()
    },
  })
})

const searchFormModel = reactive<{
  S_EQ_t_wm_bill_detail__Art_ID?: string;
  S_LIKE_t_article__Art_Name?: string;
}>({})

// 合计
const tableSummary = computed(() => {
  return [{
    index: 0,
    colSpan: 7,
    label: '合计：',
    style: {}
  }, {
    index: 6,
    colSpan: 1,
    label: billDetailTableModel.dataSourceExtra.sumAmount,
    style: {
      textAlign: 'right'
    }
  }]
})

// 使用 watchEffect 监听 props 变化，避免重复调用
watchEffect(async () => {
  // 重置分页到第一页
  billDetailTableModel.pagination.current = 1
  await billDetailTableModel.loadDataSource()
})

const reload = async (confirmDeliver?: any) => {
  console.log('reload', confirmDeliver, billDetailTableModel.dataSource.length)
  // reload 方法不再重复调用 loadDataSource，因为 watchEffect 已经处理了
  // 只处理确认发药的逻辑
  if (confirmDeliver && billDetailTableModel.dataSource && billDetailTableModel.dataSource.length > 0) {
    Modal.confirm({
      title: '库存已锁定，是否直接发药?',
      async onOk () {
        await onDeliver()
      },
      onCancel() {
      },
      class: 'test',
    });
  } else {
    // 如果需要强制刷新，重置分页并重新加载
    billDetailTableModel.pagination.current = 1
    await billDetailTableModel.loadDataSource()
  }
}

const clearData = async () => {
  billDetailTableModel.dataSource = []
  billDetailTableModel.dataSourceExtra = {}

  // 发射数据加载完成事件
  emit('dataLoaded')
}

const emit = defineEmits(['ok', 'refresh', 'dataLoaded'])

// 发药确认
const onDeliver = async () => {
  btnLoading.value = true
  try {
    await reqDeliverBySectionApi({
      wbSeqids: props.wbSeqids
    })
    
    // 判断是否需要弹出多单据扫码
    if (scanTrackCode.value) {
      billsTrackCodeRef.value?.open(props.wbSeqids)
    } else {
      printBills()
    }
    
    //弹窗是否打印

    // message.success('操作成功')
    // // 只刷新左侧表格
    // emit('refresh')
  } catch (err) {
    console.error('发药确认失败:', err)
    message.error('发药确认失败')
  } finally {
    btnLoading.value = false
  }
}

const printBills = () => {
  Modal.confirm({
    title: '发药成功，是否打印申领汇总单?',
    icon: createVNode(ExclamationCircleOutlined),
    async onOk () {
      try {
        // 打印
        await printSectionSummary();
      } catch (error) {
        message.error('打印失败，请重试');
      } finally {
        message.destroy(); // 关闭加载中提示
        emit('refresh'); // 刷新左侧表格
      }
    },
    onCancel() {
      // 只刷新左侧表格
      emit('refresh')
    },
    class: 'test',
  });
}

// 多单据扫码成功回调
const onBillsTrackCodeSuccess = () => {
  printBills()
}

// 多单据扫码取消回调
const onBillsTrackCodeCancel = () => {
  printBills()
}

const loadSectionMegerPrintHeader = async () => {
  const {data} = await sectionMegerPrintHeaderApi({
    deptCode: props.deptCode,
    sectionId: props.sectionId
  })
  return data
}

//打印-按品种汇总
const printSectionSummary = async () => {
  console.log('打印病区已发药汇总单')
  const printHeader = await loadSectionMegerPrintHeader()
  const {data} = await sectionDeliveredDetailListApi({
    wbSeqids: props.wbSeqids,
    groupByArt: 1
  })
  const billData = {
    ...printHeader,
    details: data
  }
  const template = await getPrintTemplateFullApi(PrintTemplateType.section_meger_reqart)
  if (template.tempItems) {
    template.tempItems = JSON.parse(template.tempItems);
  }
  console.log(billData)
  const printData = [{
    ...billData
  }]
  lodop.preview(template, printData)
}

// 获取数据数量的方法 - 使用 pagination.total 获取正确的总数统计
const getDataCount = () => {
  const count = billDetailTableModel.pagination.total
  console.log('patient-new 待出库明细 getDataCount 被调用 - 使用 pagination.total:', count)
  return count
}

defineExpose({
  reload,
  clearData,
  getDataCount
})
</script>

<template>
  <base-table 
    :loading="billDetailTableModel.loading" 
    :columns="billDetailTableModel.columns"
    :height="600"
    :dataSource="billDetailTableModel.dataSource"
    :pagination="billDetailTableModel.pagination"
    :summary="tableSummary"
    :rowKey="(item: DetailRecord) => item.artId + '-' + item.batchNo + '-' + item.packPrice">
    <template #btns>
      <a-space size="middle" class="m-l-10px">
        <a-space>
          <text>条目ID:</text>
          <a-input 
            v-model:value="searchFormModel.S_EQ_t_wm_bill_detail__Art_ID" 
            style="width: 100px" 
            @pressEnter="reload"/>
        </a-space>
        <a-space>
          <text>品名:</text>
          <a-input 
            v-model:value="searchFormModel.S_LIKE_t_article__Art_Name" 
            style="width: 200px" 
            @pressEnter="reload"/>
        </a-space>
        <a-button 
          :loading="btnLoading" 
          :disabled="billDetailTableModel.dataSource.length === 0" 
          type="primary" 
          @click="onDeliver()">发药确认</a-button>
      </a-space>
    </template>
    <template #bodyCell="{ column, record, index }">
      <template v-if="column?.dataIndex === 'index'">
        {{ index + 1 }}
      </template>
      <template v-if="column?.dataIndex === 'name'">
        {{ record.artName }} {{ record.artSpec }}<br/>{{ record.producer }}
      </template>
      <template v-if="column?.dataIndex === 'totalPacks'">
        <span v-if="record.totalPacks">{{ record.totalPacks }}{{ record.packUnit }}</span>
        <span v-if="record.totalCells">{{ record.totalCells }}{{ record.cellUnit }}</span>
      </template>
      <template v-if="column?.dataIndex === 'packPrice'">
        <span v-if="record.totalPacks">{{ record.packPrice }}</span>
        <span v-if="record.totalCells">{{ record.cellPrice }}</span>
      </template>
      <template v-if="column?.dataIndex === 'currentTotal'">
        <span v-if="record.deptTotalPacks">{{ record.deptTotalPacks }}{{ record.packUnit }}</span>
        <span v-if="record.deptTotalCells">{{ record.deptTotalCells }}{{ record.cellUnit }}</span>
      </template>
    </template>
  </base-table>

  <!-- 多单据追溯码扫描组件 -->
  <BillsTrackCode 
    ref="billsTrackCodeRef"
    @success="onBillsTrackCodeSuccess"
    @cancel="onBillsTrackCodeCancel" />
</template>

<style lang="less" scoped>
.bg-fff {
  background-color: #fff;
  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }
  :deep(.ant-tabs-nav-list) {
    margin-left: 10px;
  }
}
</style> 